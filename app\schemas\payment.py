"""
Pydantic schemas for payment transaction API validation.
"""
from pydantic import BaseModel, Field, EmailStr, field_validator, ConfigDict
from typing import Optional, Dict, Any
from datetime import datetime
from decimal import Decimal
import uuid

from app.models.payment import PaymentStatus, PaymentMethod, Currency


class PaymentTransactionBase(BaseModel):
    """Base schema for payment transactions."""
    
    amount: Decimal = Field(..., gt=0, description="Transaction amount (must be positive)")
    currency: Currency = Field(default=Currency.USD, description="Transaction currency")
    payment_method: PaymentMethod = Field(..., description="Payment method used")
    payment_gateway: Optional[str] = Field(None, max_length=50, description="Payment gateway name")
    
    customer_id: Optional[str] = Field(None, max_length=100, description="Customer identifier")
    customer_email: Optional[EmailStr] = Field(None, description="Customer email address")
    customer_name: Optional[str] = Field(None, max_length=255, description="Customer full name")
    
    merchant_id: Optional[str] = Field(None, max_length=100, description="Merchant identifier")
    merchant_name: Optional[str] = Field(None, max_length=255, description="Merchant name")
    
    order_id: Optional[str] = Field(None, max_length=100, description="Order identifier")
    invoice_id: Optional[str] = Field(None, max_length=100, description="Invoice identifier")
    description: Optional[str] = Field(None, description="Transaction description")
    
    fee_amount: Optional[Decimal] = Field(None, ge=0, description="Fee amount")
    tax_amount: Optional[Decimal] = Field(None, ge=0, description="Tax amount")
    
    is_test_transaction: bool = Field(default=False, description="Whether this is a test transaction")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")
    
    ip_address: Optional[str] = Field(None, max_length=45, description="Customer IP address")
    country_code: Optional[str] = Field(None, max_length=2, description="Country code (ISO 3166-1 alpha-2)")
    
    @field_validator('country_code')
    @classmethod
    def validate_country_code(cls, v):
        if v is not None and len(v) != 2:
            raise ValueError('Country code must be exactly 2 characters')
        return v.upper() if v else v


class PaymentTransactionCreate(PaymentTransactionBase):
    """Schema for creating a new payment transaction."""
    
    gateway_transaction_id: Optional[str] = Field(None, max_length=100, description="Gateway transaction ID")
    
    class Config:
        schema_extra = {
            "example": {
                "amount": 99.99,
                "currency": "USD",
                "payment_method": "credit_card",
                "payment_gateway": "stripe",
                "customer_id": "cust_123456",
                "customer_email": "<EMAIL>",
                "customer_name": "John Doe",
                "merchant_id": "merch_789",
                "merchant_name": "Example Store",
                "order_id": "order_001",
                "description": "Purchase of premium subscription",
                "fee_amount": 2.99,
                "tax_amount": 8.00,
                "is_test_transaction": False,
                "ip_address": "***********",
                "country_code": "US"
            }
        }


class PaymentTransactionUpdate(BaseModel):
    """Schema for updating a payment transaction."""
    
    status: Optional[PaymentStatus] = Field(None, description="Transaction status")
    gateway_transaction_id: Optional[str] = Field(None, max_length=100, description="Gateway transaction ID")
    processed_at: Optional[datetime] = Field(None, description="Processing timestamp")
    failure_reason: Optional[str] = Field(None, description="Failure reason if transaction failed")
    refund_reason: Optional[str] = Field(None, description="Refund reason if transaction was refunded")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")
    
    class Config:
        schema_extra = {
            "example": {
                "status": "completed",
                "gateway_transaction_id": "txn_1234567890",
                "processed_at": "2023-12-07T10:30:00Z"
            }
        }


class PaymentTransactionResponse(PaymentTransactionBase):
    """Schema for payment transaction responses."""
    
    id: uuid.UUID = Field(..., description="Unique transaction identifier")
    transaction_id: str = Field(..., description="Human-readable transaction ID")
    status: PaymentStatus = Field(..., description="Current transaction status")
    gateway_transaction_id: Optional[str] = Field(None, description="Gateway transaction ID")
    net_amount: Optional[Decimal] = Field(None, description="Net amount after fees and taxes")
    
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")
    processed_at: Optional[datetime] = Field(None, description="Processing timestamp")
    
    failure_reason: Optional[str] = Field(None, description="Failure reason if applicable")
    refund_reason: Optional[str] = Field(None, description="Refund reason if applicable")
    
    model_config = ConfigDict(
        from_attributes=True,
        json_schema_extra={
            "example": {
                "id": "123e4567-e89b-12d3-a456-426614174000",
                "transaction_id": "TXN-2023-001234",
                "amount": 99.99,
                "currency": "USD",
                "payment_method": "credit_card",
                "payment_gateway": "stripe",
                "gateway_transaction_id": "txn_1234567890",
                "status": "completed",
                "customer_id": "cust_123456",
                "customer_email": "<EMAIL>",
                "customer_name": "John Doe",
                "net_amount": 89.00,
                "created_at": "2023-12-07T10:00:00Z",
                "updated_at": "2023-12-07T10:30:00Z",
                "processed_at": "2023-12-07T10:30:00Z"
            }
        }
    )


class PaymentTransactionList(BaseModel):
    """Schema for paginated payment transaction lists."""
    
    transactions: list[PaymentTransactionResponse] = Field(..., description="List of transactions")
    total: int = Field(..., description="Total number of transactions")
    page: int = Field(..., description="Current page number")
    per_page: int = Field(..., description="Number of items per page")
    total_pages: int = Field(..., description="Total number of pages")
    
    class Config:
        schema_extra = {
            "example": {
                "transactions": [],
                "total": 150,
                "page": 1,
                "per_page": 20,
                "total_pages": 8
            }
        }


class PaymentTransactionStats(BaseModel):
    """Schema for payment transaction statistics."""
    
    total_transactions: int = Field(..., description="Total number of transactions")
    total_amount: Decimal = Field(..., description="Total transaction amount")
    completed_transactions: int = Field(..., description="Number of completed transactions")
    failed_transactions: int = Field(..., description="Number of failed transactions")
    pending_transactions: int = Field(..., description="Number of pending transactions")
    average_amount: Decimal = Field(..., description="Average transaction amount")
    
    class Config:
        schema_extra = {
            "example": {
                "total_transactions": 1250,
                "total_amount": 125000.50,
                "completed_transactions": 1100,
                "failed_transactions": 50,
                "pending_transactions": 100,
                "average_amount": 100.00
            }
        }
