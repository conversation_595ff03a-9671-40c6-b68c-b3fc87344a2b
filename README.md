# Payment Transaction Server

A robust Python server for managing payment transactions with PostgreSQL database integration. Built with FastAPI, SQLAlchemy, and comprehensive error handling.

## Features

- 🚀 **FastAPI** - Modern, fast web framework with automatic API documentation
- 🗄️ **PostgreSQL** - Robust database with full transaction support
- 📊 **Comprehensive Logging** - Structured logging with JSON support
- 🔍 **Health Monitoring** - Built-in health checks and metrics
- ✅ **Input Validation** - Pydantic schemas with comprehensive validation
- 🔒 **Error Handling** - Proper HTTP status codes and error responses
- 📈 **Performance Monitoring** - Request timing and performance metrics
- 🧪 **Sample Data** - Scripts for database initialization and seeding

## Project Structure

```
backend payment server/
├── app/
│   ├── api/
│   │   ├── endpoints/
│   │   │   ├── payments.py      # Payment transaction endpoints
│   │   │   └── health.py        # Health check endpoints
│   │   └── dependencies.py      # API dependencies
│   ├── core/
│   │   ├── exceptions.py        # Custom exceptions
│   │   ├── logging.py          # Logging configuration
│   │   ├── monitoring.py       # Metrics and monitoring
│   │   └── validators.py       # Custom validators
│   ├── database/
│   │   ├── base.py             # Database configuration
│   │   └── connection.py       # Connection management
│   ├── models/
│   │   └── payment.py          # SQLAlchemy models
│   ├── schemas/
│   │   └── payment.py          # Pydantic schemas
│   ├── services/
│   │   └── payment_service.py  # Business logic
│   ├── config.py               # Configuration management
│   └── main.py                 # FastAPI application
├── scripts/
│   ├── init_db.py              # Database initialization
│   ├── seed_data.py            # Sample data creation
│   └── reset_db.py             # Database reset
├── requirements.txt            # Python dependencies
├── .env.example               # Environment variables template
└── README.md                  # This file
```

## Quick Start

### 1. Prerequisites

- Python 3.8+
- PostgreSQL 12+
- pip or conda

### 2. Installation

1. **Clone or create the project directory:**
   ```bash
   cd "backend payment server"
   ```

2. **Create a virtual environment:**

   **On Windows (Command Prompt):**
   ```cmd
   python -m venv venv
   venv\Scripts\activate
   ```

   **On Windows (PowerShell):**
   ```powershell
   python -m venv venv
   venv\Scripts\Activate.ps1
   ```

   **On Linux/macOS:**
   ```bash
   python -m venv venv
   source venv/bin/activate
   ```

3. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

4. **Set up environment variables:**
   ```bash
   cp .env.example .env
   # Edit .env with your database credentials
   ```

### 3. Database Setup

1. **Create PostgreSQL database and schema:**
   ```sql
   -- If using existing DEV database, just create the schema
   CREATE SCHEMA IF NOT EXISTS payments;

   -- Or create a new database if needed
   CREATE DATABASE DEV;
   CREATE USER payment_user WITH PASSWORD 'your_password';
   GRANT ALL PRIVILEGES ON DATABASE DEV TO payment_user;
   GRANT ALL PRIVILEGES ON SCHEMA payments TO payment_user;
   ```

2. **Update .env file:**
   ```env
   DATABASE_URL=postgresql://postgres:your_password@localhost:5432/DEV
   DB_HOST=localhost
   DB_PORT=5432
   DB_NAME=DEV
   DB_USER=postgres
   DB_PASSWORD=your_password
   DB_SCHEMA=payments
   ```

3. **Initialize database:**
   ```bash
   python scripts/init_db.py
   ```

4. **Add sample data (optional):**
   ```bash
   python scripts/seed_data.py
   ```

### 4. Run the Server

```bash
python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8001
```

Or simply:
```bash
python run_server.py
```

The server will start at `http://localhost:8001`

## API Documentation

Once the server is running, you can access:

- **Interactive API docs (Swagger UI):** http://localhost:8001/docs
- **Alternative API docs (ReDoc):** http://localhost:8001/redoc
- **Health check:** http://localhost:8001/api/v1/health

## API Endpoints

### Payment Transactions

| Method | Endpoint | Description |
|--------|----------|-------------|
| POST | `/api/v1/payments/` | Create a new payment transaction |
| GET | `/api/v1/payments/` | List payment transactions with filtering |
| GET | `/api/v1/payments/{id}` | Get payment transaction by UUID |
| GET | `/api/v1/payments/transaction/{transaction_id}` | Get payment transaction by transaction ID |
| PUT | `/api/v1/payments/{id}` | Update payment transaction |
| DELETE | `/api/v1/payments/{id}` | Cancel payment transaction |
| GET | `/api/v1/payments/stats/summary` | Get payment statistics |

### Health & Monitoring

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/api/v1/health/` | Comprehensive health check |
| GET | `/api/v1/health/ready` | Readiness check |
| GET | `/api/v1/health/live` | Liveness check |

## Usage Examples

### Create a Payment Transaction

```bash
curl -X POST "http://localhost:8001/api/v1/payments/" \
  -H "Content-Type: application/json" \
  -d '{
    "amount": 99.99,
    "currency": "USD",
    "payment_method": "credit_card",
    "payment_gateway": "stripe",
    "customer_id": "cust_123456",
    "customer_email": "<EMAIL>",
    "customer_name": "John Doe",
    "merchant_id": "merch_789",
    "merchant_name": "Example Store",
    "order_id": "order_001",
    "description": "Purchase of premium subscription",
    "fee_amount": 2.99,
    "tax_amount": 8.00
  }'
```

### Get Payment Transactions

```bash
# Get all transactions
curl "http://localhost:8001/api/v1/payments/"

# Get transactions with filtering
curl "http://localhost:8001/api/v1/payments/?status=completed&limit=10&skip=0"

# Get transactions for a specific customer
curl "http://localhost:8001/api/v1/payments/?customer_id=cust_123456"
```

### Update Transaction Status

```bash
curl -X PUT "http://localhost:8001/api/v1/payments/{transaction_id}" \
  -H "Content-Type: application/json" \
  -d '{
    "status": "completed",
    "gateway_transaction_id": "txn_1234567890"
  }'
```

## Configuration

The application uses environment variables for configuration. Key settings:

| Variable | Description | Default |
|----------|-------------|---------|
| `DATABASE_URL` | PostgreSQL connection URL | - |
| `HOST` | Server host | 0.0.0.0 |
| `PORT` | Server port | 8001 |
| `DEBUG` | Debug mode | False |
| `LOG_LEVEL` | Logging level | INFO |
| `LOG_FORMAT` | Log format (json/text) | json |
| `SECRET_KEY` | JWT secret key | - |

## Database Schema

The main table `payment_transactions` includes:

- **Transaction Details:** ID, amount, currency, status
- **Payment Info:** Method, gateway, gateway transaction ID
- **Customer Data:** ID, email, name
- **Merchant Data:** ID, name
- **Order Info:** Order ID, invoice ID, description
- **Financial:** Fee amount, tax amount, net amount
- **Metadata:** Additional data, failure/refund reasons
- **Timestamps:** Created, updated, processed dates
- **Location:** IP address, country code

## Development

### Database Management

```bash
# Reset database (WARNING: Deletes all data)
python scripts/reset_db.py

# Initialize fresh database
python scripts/init_db.py

# Add sample data
python scripts/seed_data.py
```

### Running Tests

```bash
# Install test dependencies
pip install pytest pytest-asyncio httpx

# Run tests (when implemented)
pytest
```

### Code Quality

The project follows Python best practices:
- Type hints throughout
- Comprehensive error handling
- Structured logging
- Input validation
- Performance monitoring

## Monitoring & Logging

- **Logs:** Stored in `logs/` directory
- **Metrics:** Available via health endpoints
- **Health Checks:** Database connectivity, memory usage
- **Performance:** Request timing and error rates

## Security Considerations

For production deployment:

1. Change default secret keys
2. Use environment-specific database credentials
3. Enable HTTPS
4. Configure CORS appropriately
5. Implement authentication/authorization
6. Set up proper firewall rules
7. Regular security updates

## License

This project is provided as-is for educational and development purposes.

## Support

For issues or questions:
1. Check the logs in `logs/` directory
2. Verify database connectivity
3. Review environment configuration
4. Check API documentation at `/docs`
