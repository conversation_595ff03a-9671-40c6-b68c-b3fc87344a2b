In vscode , we have powershell , so run this in the following manner -

1. python -m venv venv
2. venv\Scripts\Activate.ps1
3. If you get Execution policy error then run this-  Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
and again run venv\Scripts\Activate.ps1

--- After successful activation of python venv , if you are running it for first time or if you get any dependency issues , then run this -

pip install -r requirements.txt