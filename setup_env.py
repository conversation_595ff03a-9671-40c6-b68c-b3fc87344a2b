#!/usr/bin/env python3
"""
Helper script to set up the .env file with correct configuration.
"""
import os
from pathlib import Path

def create_env_file():
    """Create .env file with the correct configuration for DEV database and payments schema."""
    
    env_content = """# Database Configuration
DATABASE_URL=postgresql://postgres:Pramukh100@localhost:5432/DEV
DB_HOST=localhost
DB_PORT=5432
DB_NAME=DEV
DB_USER=postgres
DB_PASSWORD=Pramukh100@
DB_SCHEMA=payments

# Server Configuration
HOST=0.0.0.0
PORT=8001
DEBUG=True

# Logging
LOG_LEVEL=INFO
LOG_FORMAT=json

# Security (change in production)
SECRET_KEY=your-secret-key-here-change-in-production

# Payment Processing (for future extensions)
PAYMENT_GATEWAY_URL=https://api.payment-gateway.com
PAYMENT_GATEWAY_API_KEY=your-api-key-here
"""
    
    env_file = Path(".env")
    
    if env_file.exists():
        print("⚠️  .env file already exists!")
        response = input("Do you want to overwrite it? (y/N): ")
        if response.lower() != 'y':
            print("Setup cancelled.")
            return
    
    try:
        with open(env_file, 'w') as f:
            f.write(env_content)
        
        print("✅ .env file created successfully!")
        print("\n📝 Configuration:")
        print("   Database: DEV")
        print("   Schema: payments")
        print("   User: postgres")
        print("   Password: Pramukh100@")
        print("   Port: 8001")
        print("\n💡 Next steps:")
        print("   1. Make sure PostgreSQL is running")
        print("   2. Run: python scripts/init_db.py")
        print("   3. Run: python run_server.py")
        
    except Exception as e:
        print(f"❌ Failed to create .env file: {e}")

if __name__ == "__main__":
    create_env_file()
