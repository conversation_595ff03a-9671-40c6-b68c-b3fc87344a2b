#!/usr/bin/env python3
"""
Test script to verify the API is working correctly.
"""
import sys
import json
import requests
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_api():
    """Test the payment API endpoint."""
    
    # Test data
    test_payment = {
        "amount": 100.50,
        "currency": "USD",
        "payment_method": "credit_card",
        "payment_gateway": "stripe",
        "customer_id": "cust_123456",
        "customer_email": "<EMAIL>",
        "customer_name": "<PERSON>",
        "merchant_id": "merchant_123",
        "merchant_name": "Test Store",
        "order_id": "order_789",
        "invoice_id": "inv_456",
        "description": "Test payment transaction",
        "fee_amount": 2.50,
        "tax_amount": 8.00,
        "is_test_transaction": True,
        "metadata": {"test": "data"},
        "ip_address": "***********",
        "country_code": "US"
    }
    
    try:
        print("🧪 Testing Payment API...")
        
        # Test the health endpoint first
        print("1. Testing health endpoint...")
        health_response = requests.get("http://localhost:8001/api/v1/health")
        
        if health_response.status_code == 200:
            print("✅ Health endpoint working")
            print(f"   Response: {health_response.json()}")
        else:
            print(f"❌ Health endpoint failed: {health_response.status_code}")
            return False
        
        # Test creating a payment
        print("\n2. Testing payment creation...")
        headers = {
            "Content-Type": "application/json",
            "Accept": "application/json"
        }
        
        response = requests.post(
            "http://localhost:8001/api/v1/payments/",
            headers=headers,
            json=test_payment  # Use json parameter instead of data
        )
        
        print(f"   Status Code: {response.status_code}")
        print(f"   Response Headers: {dict(response.headers)}")
        
        if response.status_code == 201:
            print("✅ Payment creation successful!")
            payment_data = response.json()
            print(f"   Transaction ID: {payment_data.get('transaction_id')}")
            print(f"   Amount: {payment_data.get('amount')}")
            print(f"   Status: {payment_data.get('status')}")
            return True
        else:
            print(f"❌ Payment creation failed: {response.status_code}")
            try:
                error_data = response.json()
                print(f"   Error: {json.dumps(error_data, indent=2)}")
            except:
                print(f"   Raw response: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Could not connect to the server.")
        print("   Make sure the server is running on http://localhost:8001")
        print("   Run: python run_server.py")
        return False
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_api()
    sys.exit(0 if success else 1)
