#!/usr/bin/env python3
"""
Test script to verify Pydantic configuration is working correctly.
"""
import sys
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_config():
    """Test if the configuration loads without errors."""
    try:
        print("🔧 Testing Pydantic configuration...")
        
        # Test importing the config
        from app.config import settings
        print("✅ Config imported successfully")
        
        # Test accessing some settings
        print(f"   Database: {settings.db_name}")
        print(f"   Schema: {settings.db_schema}")
        print(f"   Host: {settings.host}")
        print(f"   Port: {settings.port}")
        
        # Test importing schemas
        from app.schemas.payment import PaymentTransactionBase, PaymentTransactionResponse
        print("✅ Schemas imported successfully")
        
        # Test creating a simple schema instance
        test_data = {
            "amount": 100.00,
            "payment_method": "credit_card"
        }
        
        transaction = PaymentTransactionBase(**test_data)
        print("✅ Schema validation working")
        print(f"   Amount: {transaction.amount}")
        print(f"   Payment Method: {transaction.payment_method}")
        
        print("\n🎉 All Pydantic configurations are working correctly!")
        return True
        
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_config()
    sys.exit(0 if success else 1)
