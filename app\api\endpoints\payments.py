"""
Payment transaction API endpoints.
"""
import logging
from datetime import datetime
from typing import Optional, List
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session

from app.api.dependencies import get_payment_service, validate_pagination_params
from app.services.payment_service import PaymentService
from app.schemas.payment import (
    PaymentTransactionCreate,
    PaymentTransactionUpdate,
    PaymentTransactionResponse,
    PaymentTransactionList,
    PaymentTransactionStats
)
from app.models.payment import PaymentStatus, PaymentMethod
from app.database.base import get_db

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/payments", tags=["payments"])


@router.post(
    "/",
    response_model=PaymentTransactionResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Create a new payment transaction",
    description="Create a new payment transaction record in the database."
)
async def create_payment_transaction(
    transaction_data: PaymentTransactionCreate,
    payment_service: PaymentService = Depends(get_payment_service)
):
    """Create a new payment transaction."""
    try:
        transaction = payment_service.create_transaction(transaction_data)
        return PaymentTransactionResponse.model_validate(transaction)
    except Exception as e:
        logger.error(f"Failed to create payment transaction: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create payment transaction"
        )


@router.get(
    "/",
    response_model=PaymentTransactionList,
    summary="Get payment transactions",
    description="Retrieve payment transactions with optional filtering and pagination."
)
async def get_payment_transactions(
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of records to return"),
    status: Optional[PaymentStatus] = Query(None, description="Filter by payment status"),
    customer_id: Optional[str] = Query(None, description="Filter by customer ID"),
    merchant_id: Optional[str] = Query(None, description="Filter by merchant ID"),
    order_id: Optional[str] = Query(None, description="Filter by order ID"),
    payment_method: Optional[PaymentMethod] = Query(None, description="Filter by payment method"),
    start_date: Optional[datetime] = Query(None, description="Filter by start date (ISO format)"),
    end_date: Optional[datetime] = Query(None, description="Filter by end date (ISO format)"),
    sort_by: str = Query("created_at", description="Field to sort by"),
    sort_order: str = Query("desc", regex="^(asc|desc)$", description="Sort order"),
    payment_service: PaymentService = Depends(get_payment_service)
):
    """Get payment transactions with filtering and pagination."""
    try:
        # Validate pagination parameters
        skip, limit = validate_pagination_params(skip, limit)
        
        transactions, total = payment_service.get_transactions(
            skip=skip,
            limit=limit,
            status=status,
            customer_id=customer_id,
            merchant_id=merchant_id,
            order_id=order_id,
            payment_method=payment_method,
            start_date=start_date,
            end_date=end_date,
            sort_by=sort_by,
            sort_order=sort_order
        )
        
        # Calculate pagination info
        total_pages = (total + limit - 1) // limit
        current_page = (skip // limit) + 1
        
        return PaymentTransactionList(
            transactions=[PaymentTransactionResponse.model_validate(t) for t in transactions],
            total=total,
            page=current_page,
            per_page=limit,
            total_pages=total_pages
        )
    except Exception as e:
        logger.error(f"Failed to get payment transactions: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve payment transactions"
        )


@router.get(
    "/{transaction_id}",
    response_model=PaymentTransactionResponse,
    summary="Get payment transaction by ID",
    description="Retrieve a specific payment transaction by its UUID."
)
async def get_payment_transaction(
    transaction_id: UUID,
    payment_service: PaymentService = Depends(get_payment_service)
):
    """Get a payment transaction by ID."""
    try:
        transaction = payment_service.get_transaction_by_id(transaction_id)
        if not transaction:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Payment transaction not found"
            )
        return PaymentTransactionResponse.model_validate(transaction)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get payment transaction: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve payment transaction"
        )


@router.get(
    "/transaction/{transaction_id}",
    response_model=PaymentTransactionResponse,
    summary="Get payment transaction by transaction ID",
    description="Retrieve a specific payment transaction by its transaction ID string."
)
async def get_payment_transaction_by_transaction_id(
    transaction_id: str,
    payment_service: PaymentService = Depends(get_payment_service)
):
    """Get a payment transaction by transaction ID string."""
    try:
        transaction = payment_service.get_transaction_by_transaction_id(transaction_id)
        if not transaction:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Payment transaction not found"
            )
        return PaymentTransactionResponse.model_validate(transaction)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get payment transaction: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve payment transaction"
        )


@router.put(
    "/{transaction_id}",
    response_model=PaymentTransactionResponse,
    summary="Update payment transaction",
    description="Update a payment transaction's status and other fields."
)
async def update_payment_transaction(
    transaction_id: UUID,
    update_data: PaymentTransactionUpdate,
    payment_service: PaymentService = Depends(get_payment_service)
):
    """Update a payment transaction."""
    try:
        transaction = payment_service.update_transaction(transaction_id, update_data)
        if not transaction:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Payment transaction not found"
            )
        return PaymentTransactionResponse.model_validate(transaction)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to update payment transaction: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update payment transaction"
        )


@router.delete(
    "/{transaction_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="Delete payment transaction",
    description="Soft delete a payment transaction by marking it as cancelled."
)
async def delete_payment_transaction(
    transaction_id: UUID,
    payment_service: PaymentService = Depends(get_payment_service)
):
    """Delete (cancel) a payment transaction."""
    try:
        success = payment_service.delete_transaction(transaction_id)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Payment transaction not found"
            )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to delete payment transaction: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete payment transaction"
        )


@router.get(
    "/stats/summary",
    response_model=PaymentTransactionStats,
    summary="Get payment transaction statistics",
    description="Get aggregated statistics for payment transactions."
)
async def get_payment_transaction_stats(
    start_date: Optional[datetime] = Query(None, description="Start date for statistics"),
    end_date: Optional[datetime] = Query(None, description="End date for statistics"),
    merchant_id: Optional[str] = Query(None, description="Filter by merchant ID"),
    payment_service: PaymentService = Depends(get_payment_service)
):
    """Get payment transaction statistics."""
    try:
        stats = payment_service.get_transaction_stats(
            start_date=start_date,
            end_date=end_date,
            merchant_id=merchant_id
        )
        return stats
    except Exception as e:
        logger.error(f"Failed to get payment transaction stats: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve payment transaction statistics"
        )
