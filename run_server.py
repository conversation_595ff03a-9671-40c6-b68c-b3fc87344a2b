#!/usr/bin/env python3
"""
Simple script to run the payment server.
"""
import sys
import os
import logging
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app.config import settings

def main():
    """Run the payment server."""
    try:
        # Use a different port to avoid conflicts
        server_port = 8001  # Changed from default 8000 to avoid conflicts

        print("🚀 Starting Payment Transaction Server...")
        print(f"📍 Server will run on: http://{settings.host}:{server_port}")
        print(f"📚 API Documentation: http://{settings.host}:{server_port}/docs")
        print(f"🏥 Health Check: http://{settings.host}:{server_port}/api/v1/health")
        print()

        # Import and run the server
        import uvicorn

        uvicorn.run(
            "app.main:app",
            host=settings.host,
            port=server_port,
            reload=settings.debug,
            log_level=settings.log_level.lower()
        )
        
    except KeyboardInterrupt:
        print("\n👋 Server stopped by user")
    except Exception as e:
        print(f"❌ Failed to start server: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
