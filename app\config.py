"""
Configuration management for the payment server.
"""
import os
from typing import Op<PERSON>
from pydantic_settings import BaseSettings, SettingsConfigDict
from pydantic import Field


class Settings(BaseSettings):
    """Application settings loaded from environment variables."""
    
    # Database Configuration
    database_url: str = Field(
        default="postgresql://postgres:password@localhost:5432/DEV",
        description="PostgreSQL database URL"
    )
    db_host: str = Field(default="localhost", description="Database host")
    db_port: int = Field(default=5432, description="Database port")
    db_name: str = Field(default="DEV", description="Database name")
    db_user: str = Field(default="postgres", description="Database username")
    db_password: str = Field(default="password", description="Database password")
    db_schema: str = Field(default="payments", description="Database schema")
    
    # Server Configuration
    host: str = Field(default="0.0.0.0", description="Server host")
    port: int = Field(default=8001, description="Server port")
    debug: bool = Field(default=False, description="Debug mode")
    
    # Security
    secret_key: str = Field(
        default="your-secret-key-here-change-in-production",
        description="Secret key for JWT tokens"
    )
    algorithm: str = Field(default="HS256", description="JWT algorithm")
    access_token_expire_minutes: int = Field(
        default=30,
        description="Access token expiration time in minutes"
    )
    
    # Logging
    log_level: str = Field(default="INFO", description="Logging level")
    log_format: str = Field(default="json", description="Log format (json or text)")
    
    # Payment Gateway (for future extensions)
    payment_gateway_url: Optional[str] = Field(
        default=None,
        description="Payment gateway API URL"
    )
    payment_gateway_api_key: Optional[str] = Field(
        default=None,
        description="Payment gateway API key"
    )
    
    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False
    )


# Global settings instance
settings = Settings()


def get_database_url() -> str:
    """Get the complete database URL."""
    if settings.database_url and settings.database_url != "postgresql://postgres:password@localhost:5432/DEV":
        return settings.database_url

    return f"postgresql://{settings.db_user}:{settings.db_password}@{settings.db_host}:{settings.db_port}/{settings.db_name}"
