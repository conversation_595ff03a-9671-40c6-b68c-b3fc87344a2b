# Database Configuration
DATABASE_URL=postgresql://postgres:Pramukh100@localhost:5432/DEV
DB_HOST=localhost
DB_PORT=5432
DB_NAME=DEV
DB_USER=postgres
DB_PASSWORD=Pramukh100@

# Server Configuration
HOST=0.0.0.0
PORT=16388
DEBUG=True

# Logging
LOG_LEVEL=INFO
LOG_FORMAT=json

# Payment Processing (for future extensions)
PAYMENT_GATEWAY_URL=https://api.payment-gateway.com
PAYMENT_GATEWAY_API_KEY=your-api-key-here
